<?php
namespace Bgs\FlightLandingPages\UserFunc;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;

/**
 * UserFunc to replace lib.dynamicContent and handle virtual routes
 *
 * This class provides a drop-in replacement for lib.dynamicContent that:
 * - Automatically detects virtual routes
 * - Renders template page content for virtual routes
 * - Falls back to standard TYPO3 CONTENT object for normal pages
 * - Works with any TypoScript configuration (or lack thereof)
 *
 * This ensures existing templates work without modification and follows
 * TYPO3 best practices by using core content rendering mechanisms.
 */
class VirtualRouteContentRenderer
{
    /**
     * Render content for the current context (virtual route or normal page)
     *
     * @param string $content The content (usually empty for USER objects)
     * @param array $conf The TypoScript configuration
     * @param ContentObjectRenderer $cObj The content object renderer
     * @return string The rendered content
     */
    public function render(string $content, array $conf, ContentObjectRenderer $cObj): string
    {
        // Get column position from the data array (passed by f:cObject viewhelper)
        $colPos = (int)($cObj->data['colPos'] ?? 0);
        
        // Check if virtual route data is available
        $flightRouteData = $GLOBALS['TYPO3_CONF_VARS']['USER']['flightRouteData'] ?? null;
        
        if ($flightRouteData && ($flightRouteData['isVirtualRoute'] ?? false)) {
            // Virtual route - render template page content
            return $this->renderVirtualRouteContent($colPos, $flightRouteData, $cObj);
        } else {
            // Normal page - use standard TYPO3 content rendering
            return $this->renderNormalContent($colPos, $cObj);
        }
    }

    /**
     * Render virtual route content from template page
     */
    protected function renderVirtualRouteContent(int $colPos, array $flightRouteData, ContentObjectRenderer $cObj): string
    {
        $templateContent = $flightRouteData['templatePageContent'] ?? [];
        
        if (empty($templateContent)) {
            return '';
        }
        
        // Filter content by column position
        $columnContent = array_filter($templateContent, function($element) use ($colPos) {
            return (int)($element['colPos'] ?? 0) === $colPos;
        });
        
        if (empty($columnContent)) {
            return '';
        }
        
        // Render content elements
        $output = '';
        foreach ($columnContent as $contentElement) {
            $output .= $this->renderContentElement($contentElement, $cObj);
        }
        
        return $output;
    }

    /**
     * Render normal page content using TYPO3's standard content rendering
     *
     * This method uses TYPO3's CONTENT object directly instead of relying on
     * lib.dynamicContent, which ensures compatibility with any TypoScript
     * configuration (or lack thereof).
     */
    protected function renderNormalContent(int $colPos, ContentObjectRenderer $cObj): string
    {
        // Use TYPO3's standard CONTENT object to render content elements
        // This bypasses the need for lib.dynamicContent and works in all contexts
        $conf = [
            'table' => 'tt_content',
            'select.' => [
                'orderBy' => 'sorting',
                'where' => 'colPos=' . (int)$colPos,
                'languageField' => 'sys_language_uid',
            ]
        ];

        return $cObj->cObjGetSingle('CONTENT', $conf);
    }

    /**
     * Render a single content element
     */
    protected function renderContentElement(array $contentData, ContentObjectRenderer $cObj): string
    {
        if (empty($contentData)) {
            return '';
        }

        $cType = $contentData['CType'] ?? 'text';
        
        // Basic rendering based on CType
        switch ($cType) {
            case 'text':
            case 'textmedia':
                return $this->renderTextElement($contentData);
                
            case 'header':
                return $this->renderHeaderElement($contentData);
                
            case 'html':
                return $this->renderHtmlElement($contentData);
                
            case 'image':
                return $this->renderImageElement($contentData);
                
            default:
                return $this->renderGenericElement($contentData);
        }
    }

    /**
     * Render text/textmedia content element
     */
    protected function renderTextElement(array $data): string
    {
        $output = '';
        
        // Header
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        // Subheader
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        // Bodytext
        if (!empty($data['bodytext'])) {
            // For HTML content, don't escape if it contains HTML tags
            if (strip_tags($data['bodytext']) !== $data['bodytext']) {
                $output .= '<div class="bodytext">' . $data['bodytext'] . '</div>';
            } else {
                $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($data['bodytext'])) . '</div>';
            }
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($data['CType']) . '">' . $output . '</div>';
    }

    /**
     * Render header content element
     */
    protected function renderHeaderElement(array $data): string
    {
        if (empty($data['header'])) {
            return '';
        }
        
        $headerLevel = (int)($data['header_layout'] ?: 1);
        $output = '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        return '<div class="content-element content-element-header">' . $output . '</div>';
    }

    /**
     * Render HTML content element
     */
    protected function renderHtmlElement(array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        if (!empty($data['bodytext'])) {
            $output .= $data['bodytext']; // HTML content is not escaped
        }
        
        return '<div class="content-element content-element-html">' . $output . '</div>';
    }

    /**
     * Render generic content element
     */
    protected function renderGenericElement(array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        if (!empty($data['bodytext'])) {
            // For HTML content, don't escape if it contains HTML tags
            if (strip_tags($data['bodytext']) !== $data['bodytext']) {
                $output .= '<div class="bodytext">' . $data['bodytext'] . '</div>';
            } else {
                $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($data['bodytext'])) . '</div>';
            }
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($data['CType']) . '">' . $output . '</div>';
    }
}
