<?php
namespace Bgs\FlightLandingPages\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Http\Response;
use TYPO3\CMS\Core\Context\Context;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Frontend\Authentication\FrontendUserAuthentication;
use Bgs\FlightLandingPages\Controller\VirtualRouteFrontendController;
use Bgs\FlightLandingPages\Service\VirtualRouteRenderingService;

/**
 * Middleware to handle virtual route URLs for Flight Landing Pages
 * 
 * This middleware intercepts requests that match virtual route patterns
 * (e.g., /flights/ber-sof) and redirects them to the appropriate landing page
 * with the route information preserved for processing.
 */
class VirtualRouteMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $site = $request->getAttribute('site');

        if (!$site instanceof Site) {
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');
        
        // Check if this path matches a virtual route pattern
        $virtualRouteMatch = $this->matchVirtualRoute($path, $site);
        if ($virtualRouteMatch) {
            // Generate custom response for virtual route
            return $this->generateVirtualRouteResponse($request, $virtualRouteMatch, $site);
        }

        return $handler->handle($request);
    }

    /**
     * Check if the path matches a virtual route pattern
     */
    protected function matchVirtualRoute(string $path, Site $site): ?array
    {
        // Extract potential route pattern from path
        $pathParts = explode('/', $path);

        if (count($pathParts) < 2) {
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ber-sof")
        $routeSlug = end($pathParts);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);

        // Ensure path starts with /
        $searchPath = '/' . ltrim($landingPagePath, '/');
        $searchPath = str_replace($site->getBase()->getPath(), '', $searchPath);
        $searchPathLP = ltrim($searchPath, '/');

        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($searchPath, $site);

        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $flightRoute = $this->findFlightRoute("{$searchPathLP}/{$routeSlug}", $landingPageData['uid']);

        if (!$flightRoute) {
            return null;
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => "{$searchPathLP}/{$routeSlug}",
            'landingPagePath' => $landingPagePath
        ];
    }

    /**
     * Find landing page by path within the site
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Build the path condition - we need to find pages that match the path
        $siteRootPageId = $site->getRootPageId();



        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($path))
            )
            ->executeQuery();

        $pages = $result->fetchAllAssociative();
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Check if a page belongs to a specific site
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Get the page's root line to check if it belongs to this site
        $currentPageUid = $pageUid;
        
        while ($currentPageUid > 0) {
            if ($currentPageUid === $siteRootPageId) {
                return true;
            }
            
            $result = $queryBuilder
                ->select('pid')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($currentPageUid, \PDO::PARAM_INT))
                )
                ->executeQuery();
            
            $page = $result->fetchAssociative();
            if (!$page) {
                break;
            }
            
            $currentPageUid = (int)$page['pid'];
        }

        return false;
    }

    /**
     * Find flight route by origin/destination codes and landing page
     */
    protected function findFlightRoute(string $routeSlug,int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('route_slug', $queryBuilder->createNamedParameter($routeSlug)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Generate a custom response for virtual routes using the custom controller
     */
    protected function generateVirtualRouteResponse(ServerRequestInterface $request, array $virtualRouteMatch, Site $site): ResponseInterface
    {
        $landingPage = $virtualRouteMatch['landingPage'];

        try {
            // Get the current context
            $context = GeneralUtility::makeInstance(Context::class);

            // Get site language (default language)
            $siteLanguage = $site->getDefaultLanguage();

            // Create page arguments for the landing page
            $pageArguments = new PageArguments($landingPage['uid'], '0', []);

            // Create frontend user authentication
            $frontendUser = GeneralUtility::makeInstance(FrontendUserAuthentication::class);

            // Initialize our custom virtual route frontend controller
            $controller = new VirtualRouteFrontendController(
                $context,
                $site,
                $siteLanguage,
                $pageArguments,
                $frontendUser
            );

            // Set the virtual route data for processing
            $controller->setVirtualRouteData($virtualRouteMatch);

            // Create a modified request that points to the landing page
            $uri = $request->getUri();
            $newPath = $landingPage['slug'];

            // Ensure the path starts with /
            if (!str_starts_with($newPath, '/')) {
                $newPath = '/' . $newPath;
            }

            $newUri = $uri->withPath($newPath);
            $modifiedRequest = $request->withUri($newUri);

            // Add virtual route information as request attributes
            $modifiedRequest = $modifiedRequest->withAttribute('flightLandingPages.virtualRoute', $virtualRouteMatch);

            // Set the controller in GLOBALS (required for TYPO3's rendering pipeline)
            $GLOBALS['TSFE'] = $controller;
            $GLOBALS['TYPO3_REQUEST'] = $modifiedRequest;

            // Initialize the controller using TYPO3's standard process
            $controller->determineId($modifiedRequest);
            $controller->getFromCache($modifiedRequest);

            // Create content object renderer
            $controller->newCObj($modifiedRequest);

            // Prepare page content generation (this will process virtual route content)
            $controller->preparePageContentGeneration($modifiedRequest);

            // Render the page using TYPO3's standard PAGE object pipeline
            // This ensures virtual routes use the same templates and rendering as normal pages
            if (is_array($controller->pSetup)) {
                try {
                    // Use TYPO3's standard rendering pipeline - this will automatically
                    // include any configured templates, layouts, and TypoScript
                    $pageContent = $controller->cObj->cObjGet($controller->pSetup);
                } catch (\Exception $e) {
                    // If TypoScript rendering fails, fall back to simple content
                    $pageContent = '<html><body><h1>TypoScript Error</h1><p>Error: ' . htmlspecialchars($e->getMessage()) . '</p></body></html>';
                }
            } else {
                $pageContent = '<html><body><h1>No TypoScript configuration found</h1><p>Please ensure your site has a TypoScript template.</p></body></html>';
            }

            // Create response with the generated content
            $response = new Response();
            $response->getBody()->write($pageContent);

            return $response->withHeader('Content-Type', 'text/html; charset=utf-8');

        } catch (\Exception $e) {
            // Fallback: return 404 if page generation fails
            $response = new Response();
            $response->getBody()->write('Page not found: ' . $e->getMessage());
            return $response->withStatus(404);
        }
    }
}
