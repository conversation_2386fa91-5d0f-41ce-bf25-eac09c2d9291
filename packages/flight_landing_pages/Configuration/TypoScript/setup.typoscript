# Flight Landing Pages Extension Setup

# Import fluid_styled_content for content element rendering
@import 'EXT:fluid_styled_content/Configuration/TypoScript/setup.typoscript'

# Configuration for Flight Template Pages (doktype 200)
# These pages render normally but are typically not linked in navigation
# They serve as content templates for Flight Landing Pages
[page["doktype"] == 200]
    # Template pages render normally as standard TYPO3 pages
    # No special configuration needed - they inherit from standard page configuration
[END]

# Global configuration for virtual routes and flight landing pages
# This ensures flight route data is available for both normal landing pages and virtual routes
page.10.dataProcessing {
    # Add virtual route data processor for all pages
    # This will provide flight route data when available (virtual routes or landing pages)
    1000 = Bgs\FlightLandingPages\DataProcessing\VirtualRouteDataProcessor
    1000 {
        as = flightRouteData
    }
}

# Define lib.dynamicContent to handle both virtual routes and normal content
# This ensures that existing templates work without modification
lib.dynamicContent = USER
lib.dynamicContent {
    userFunc = Bgs\FlightLandingPages\UserFunc\VirtualRouteContentRenderer->render
}

# Also provide a fallback CONTENT object for lib.dynamicContent
# This ensures compatibility if the UserFunc approach doesn't work
lib.dynamicContent.fallback = CONTENT
lib.dynamicContent.fallback {
    table = tt_content
    select {
        orderBy = sorting
        where = colPos={register:colPos}
        where.insertData = 1
        languageField = sys_language_uid
    }
}





# Plugin configuration for FlightReference list (when used as content element)
plugin.tx_flightlandingpages {
    view {
        templateRootPaths {
            0 = EXT:flight_landing_pages/Resources/Private/Templates/
            1 = {$plugin.tx_flightlandingpages.view.templateRootPath}
        }
        partialRootPaths {
            0 = EXT:flight_landing_pages/Resources/Private/Partials/
            1 = {$plugin.tx_flightlandingpages.view.partialRootPath}
        }
        layoutRootPaths {
            0 = EXT:flight_landing_pages/Resources/Private/Layouts/
            1 = {$plugin.tx_flightlandingpages.view.layoutRootPath}
        }
    }

    persistence {
        storagePid = {$plugin.tx_flightlandingpages.persistence.storagePid}
    }

    settings {
        # API configuration for flight data
        api {
            baseUrl = https://api.example.com/flights
            apiKey =
            cacheLifetime = 3600
        }
    }
}

# Include CSS and JS only for flight landing pages
[page["doktype"] == 201]
    page {
        includeCSS {
            flightlandingpages = EXT:flight_landing_pages/Resources/Public/CSS/styles.css
        }
        includeJSFooter {
            flightlandingpages = EXT:flight_landing_pages/Resources/Public/JavaScript/main.js
        }
    }
[END]
