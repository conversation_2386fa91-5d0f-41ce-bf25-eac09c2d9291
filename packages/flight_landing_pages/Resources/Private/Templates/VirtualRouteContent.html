<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}

<!--
Virtual Route Content Template

This template replaces lib.dynamicContent and automatically handles:
- Virtual routes: Renders template page content with flight data
- Normal pages: Renders standard TYPO3 content elements

The template uses the VirtualRouteContentViewHelper which automatically
detects whether this is a virtual route or normal page and renders
the appropriate content.
-->

<flp:virtualRouteContent colPos="{colPos}" flightRouteData="{flightRouteData}" />

</html>
