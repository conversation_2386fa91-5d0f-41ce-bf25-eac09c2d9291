<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Domain\Model;

/**
 * Entity object to transfer virtual route data between middlewares
 * 
 * This immutable object encapsulates all virtual route information needed
 * for detection, routing, and rendering phases.
 */
class VirtualRouteContext
{
    public function __construct(
        private readonly bool $isVirtualRoute,
        private readonly ?array $flightRoute = null,
        private readonly ?array $landingPage = null,
        private readonly ?string $routeSlug = null,
        private readonly ?string $originalPath = null,
        private readonly ?array $routeParameters = null,
    ) {}

    public function isVirtualRoute(): bool
    {
        return $this->isVirtualRoute;
    }

    public function getFlightRoute(): ?array
    {
        return $this->flightRoute;
    }

    public function getLandingPage(): ?array
    {
        return $this->landingPage;
    }

    public function getRouteSlug(): ?string
    {
        return $this->routeSlug;
    }

    public function getOriginalPath(): ?string
    {
        return $this->originalPath;
    }

    public function getRouteParameters(): ?array
    {
        return $this->routeParameters;
    }

    /**
     * Factory method for normal (non-virtual) routes
     */
    public static function createNormal(): self
    {
        return new self(false);
    }

    /**
     * Factory method for virtual routes
     */
    public static function createVirtual(
        array $flightRoute,
        array $landingPage,
        string $routeSlug,
        string $originalPath,
        array $routeParameters = []
    ): self {
        return new self(
            true,
            $flightRoute,
            $landingPage,
            $routeSlug,
            $originalPath,
            $routeParameters
        );
    }

    /**
     * Get cache identifier for this virtual route context
     */
    public function getCacheIdentifier(): string
    {
        if (!$this->isVirtualRoute()) {
            return 'normal';
        }

        return 'virtual_' . md5($this->routeSlug . '_' . $this->landingPage['uid']);
    }

    /**
     * Get cache tags for this virtual route context
     */
    public function getCacheTags(): array
    {
        if (!$this->isVirtualRoute()) {
            return [];
        }

        return [
            'virtual_route',
            'landing_page_' . $this->landingPage['uid'],
            'flight_route_' . $this->flightRoute['uid']
        ];
    }

    /**
     * Convert to array for global storage
     */
    public function toArray(): array
    {
        return [
            'isVirtualRoute' => $this->isVirtualRoute,
            'flightRoute' => $this->flightRoute,
            'landingPage' => $this->landingPage,
            'routeSlug' => $this->routeSlug,
            'originalPath' => $this->originalPath,
            'routeParameters' => $this->routeParameters,
        ];
    }
}
