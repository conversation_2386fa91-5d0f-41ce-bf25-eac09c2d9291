<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Middleware;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\TypoScript\FrontendTypoScript;

/**
 * Middleware 2: Virtual Route Enhancement
 * 
 * Runs AFTER PrepareTypoScriptFrontendRendering to:
 * - Check for virtual route context from Middleware 1
 * - Manipulate frontend.typoscript attribute to inject lib.dynamicContent
 * - Store virtual route data globally for UserFunc access
 */
class VirtualRouteEnhancementMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // Get virtual route context from Middleware 1
        $context = $request->getAttribute('virtualRoute.context');
        
        if (!$context instanceof VirtualRouteContext || !$context->isVirtualRoute()) {
            // Not a virtual route - continue normally
            return $handler->handle($request);
        }

        // This is a virtual route - enhance TypoScript
        $modifiedRequest = $this->enhanceTypoScriptForVirtualRoute($request, $context);
        
        // Store virtual route data globally for UserFunc access
        $this->storeVirtualRouteDataGlobally($context);
        
        return $handler->handle($modifiedRequest);
    }

    /**
     * Enhance TypoScript for virtual routes using v12+ frontend.typoscript attribute
     */
    protected function enhanceTypoScriptForVirtualRoute(ServerRequestInterface $request, VirtualRouteContext $context): ServerRequestInterface
    {
        // Get the frontend.typoscript attribute (available after PrepareTypoScriptFrontendRendering)
        $frontendTypoScript = $request->getAttribute('frontend.typoscript');
        
        if (!$frontendTypoScript instanceof FrontendTypoScript) {
            // TypoScript not available yet - this shouldn't happen if middleware is positioned correctly
            error_log('VirtualRouteEnhancementMiddleware: frontend.typoscript attribute not available');
            return $request;
        }

        // Get the setup array for manipulation
        $setup = $frontendTypoScript->getSetupArray();
        
        // Inject lib.dynamicContent override for virtual routes
        $this->injectLibDynamicContent($setup);
        
        // For now, let's try a simpler approach - just store the data globally
        // and let the UserFunc handle the lib.dynamicContent replacement
        // TODO: Implement proper frontend.typoscript manipulation when we figure out the correct API

        error_log('VirtualRouteEnhancementMiddleware: Enhanced TypoScript for virtual route');

        // Return the original request for now
        return $request;
    }

    /**
     * Inject lib.dynamicContent override for virtual routes
     */
    protected function injectLibDynamicContent(array &$setup): void
    {
        // Ensure lib. exists
        if (!isset($setup['lib.'])) {
            $setup['lib.'] = [];
        }

        // Override lib.dynamicContent with our UserFunc that handles virtual routes
        $setup['lib.']['dynamicContent'] = 'USER';
        $setup['lib.']['dynamicContent.'] = [
            'userFunc' => 'Bgs\\FlightLandingPages\\UserFunc\\VirtualRouteContentRenderer->render'
        ];

        error_log('VirtualRouteEnhancementMiddleware: Injected lib.dynamicContent override for virtual route');
    }

    /**
     * Store virtual route data globally for UserFunc access
     */
    protected function storeVirtualRouteDataGlobally(VirtualRouteContext $context): void
    {
        // Store in GLOBALS for UserFunc access (same pattern as original implementation)
        $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData'] = $context->toArray();
        
        error_log('VirtualRouteEnhancementMiddleware: Stored virtual route data globally');
    }
}
