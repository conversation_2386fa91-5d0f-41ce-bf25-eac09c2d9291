<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Middleware;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Middleware 1: Virtual Route Detection
 * 
 * Runs BEFORE PageResolver to:
 * - Detect virtual route URLs
 * - Modify request to point to template page
 * - Store virtual route context for later middleware
 */
class VirtualRouteDetectionMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        error_log('VirtualRouteDetectionMiddleware: Processing request for path: ' . $request->getUri()->getPath());

        $site = $request->getAttribute('site');

        if (!$site instanceof Site) {
            error_log('VirtualRouteDetectionMiddleware: No site found, continuing');
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');

        // Detect virtual route using the same algorithm from original middleware
        $virtualRouteMatch = $this->matchVirtualRoute($path, $site);
        
        if ($virtualRouteMatch) {
            error_log('VirtualRouteDetectionMiddleware: Found virtual route match for: ' . $path);

            // Create virtual route context entity
            $context = VirtualRouteContext::createVirtual(
                $virtualRouteMatch['flightRoute'],
                $virtualRouteMatch['landingPage'],
                $virtualRouteMatch['routeSlug'],
                $path
            );

            // Modify request to point to template page
            $landingPage = $virtualRouteMatch['landingPage'];
            $templatePageSlug = $landingPage['slug'];

            error_log('VirtualRouteDetectionMiddleware: Redirecting to template page: ' . $templatePageSlug);

            // Create new URI pointing to template page
            $newUri = $request->getUri()->withPath($templatePageSlug);
            $modifiedRequest = $request->withUri($newUri);

            // Add virtual route context to request for Middleware 2
            $modifiedRequest = $modifiedRequest->withAttribute('virtualRoute.context', $context);

            return $handler->handle($modifiedRequest);
        }

        // Not a virtual route - add normal context and continue
        $normalContext = VirtualRouteContext::createNormal();
        $request = $request->withAttribute('virtualRoute.context', $normalContext);
        
        return $handler->handle($request);
    }

    /**
     * Check if the path matches a virtual route pattern
     * Reuses the same algorithm from VirtualRouteMiddleware
     */
    protected function matchVirtualRoute(string $path, Site $site): ?array
    {
        // Extract potential route pattern from path
        $pathParts = explode('/', $path);

        if (count($pathParts) < 2) {
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ber-sof")
        $routeSlug = end($pathParts);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);

        // Ensure path starts with /
        $searchPath = '/' . ltrim($landingPagePath, '/');
        $searchPath = str_replace($site->getBase()->getPath(), '', $searchPath);
        $searchPathLP = ltrim($searchPath, '/');

        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($searchPath, $site);

        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $flightRoute = $this->findFlightRoute("{$searchPathLP}/{$routeSlug}", $landingPageData['uid']);

        if (!$flightRoute) {
            return null;
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => "{$searchPathLP}/{$routeSlug}",
            'landingPagePath' => $landingPagePath
        ];
    }

    /**
     * Find landing page by path within the site
     * Reuses the same algorithm from VirtualRouteMiddleware
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $siteRootPageId = $site->getRootPageId();

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($path))
            )
            ->executeQuery();

        $pages = $result->fetchAllAssociative();
        
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Check if a page belongs to the given site
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $currentPageUid = $pageUid;

        // Build rootline to check if page is within site
        while ($currentPageUid > 0) {
            $page = $queryBuilder
                ->select('uid', 'pid')
                ->from('pages')
                ->where($queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($currentPageUid, \PDO::PARAM_INT)))
                ->executeQuery()
                ->fetchAssociative();

            if (!$page) {
                break;
            }

            if ($page['uid'] == $siteRootPageId) {
                return true;
            }

            $currentPageUid = (int)$page['pid'];
        }

        return false;
    }

    /**
     * Find flight route by route slug and landing page
     */
    protected function findFlightRoute(string $routeSlug, int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('route_slug', $queryBuilder->createNamedParameter($routeSlug)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }
}
