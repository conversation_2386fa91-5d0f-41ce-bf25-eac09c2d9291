<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Routing;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Virtual Route Detection Middleware
 *
 * This middleware detects virtual route URLs and modifies the request to point
 * to the template page, allowing TYPO3's PageResolver to handle it normally.
 */
class VirtualRoutePageResolver implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $site = $request->getAttribute('site');

        if (!$site instanceof Site) {
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');

        // Check if this path matches a virtual route pattern
        $virtualRouteMatch = $this->matchVirtualRoute($path, $site);

        if ($virtualRouteMatch) {
            // Modify the request to point to the template page
            $landingPage = $virtualRouteMatch['landingPage'];
            $templatePageSlug = $landingPage['slug'];

            // Create new URI pointing to template page
            $newUri = $request->getUri()->withPath($templatePageSlug);
            $modifiedRequest = $request->withUri($newUri);

            // Add virtual route data as request attributes for later use
            $modifiedRequest = $modifiedRequest->withAttribute('virtualRoute.data', $virtualRouteMatch);
            $modifiedRequest = $modifiedRequest->withAttribute('virtualRoute.isVirtual', true);

            // Store virtual route data globally for UserFunc access
            $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData'] = $virtualRouteMatch;

            return $handler->handle($modifiedRequest);
        }

        // Not a virtual route - continue normally
        return $handler->handle($request);
    }

    /**
     * Check if the path matches a virtual route pattern
     * Reuses the same algorithm from VirtualRouteMiddleware
     */
    protected function matchVirtualRoute(string $path, Site $site): ?array
    {
        // Extract potential route pattern from path
        $pathParts = explode('/', $path);

        if (count($pathParts) < 2) {
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ber-sof")
        $routeSlug = end($pathParts);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);

        // Ensure path starts with /
        $searchPath = '/' . ltrim($landingPagePath, '/');
        $searchPath = str_replace($site->getBase()->getPath(), '', $searchPath);
        $searchPathLP = ltrim($searchPath, '/');

        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($searchPath, $site);

        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $flightRoute = $this->findFlightRoute("{$searchPathLP}/{$routeSlug}", $landingPageData['uid']);

        if (!$flightRoute) {
            return null;
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => "{$searchPathLP}/{$routeSlug}",
            'landingPagePath' => $landingPagePath
        ];
    }

    /**
     * Find landing page by path within the site
     * Reuses the same algorithm from VirtualRouteMiddleware
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Build the path condition - we need to find pages that match the path
        $siteRootPageId = $site->getRootPageId();

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($path))
            )
            ->executeQuery();

        $pages = $result->fetchAllAssociative();
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Check if a page belongs to the given site
     * Reuses the same algorithm from VirtualRouteMiddleware
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $rootline = [];
        $currentPageUid = $pageUid;

        // Build rootline to check if page is within site
        while ($currentPageUid > 0) {
            $page = $queryBuilder
                ->select('uid', 'pid')
                ->from('pages')
                ->where($queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($currentPageUid, \PDO::PARAM_INT)))
                ->executeQuery()
                ->fetchAssociative();

            if (!$page) {
                break;
            }

            $rootline[] = $page;

            if ($page['uid'] == $siteRootPageId) {
                return true;
            }

            $currentPageUid = (int)$page['pid'];
        }

        return false;
    }

    /**
     * Find flight route by route slug and landing page
     * Reuses the same algorithm from VirtualRouteMiddleware
     */
    protected function findFlightRoute(string $routeSlug, int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('route_slug', $queryBuilder->createNamedParameter($routeSlug)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }
}
