services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Bgs\FlightLandingPages\:
    resource: '../Classes/*'
    exclude:
      - '../Classes/Domain/Model/*'

  # Virtual Route Detection Middleware (registered via RequestMiddlewares.php)

  # Explicitly configure services that need special handling

  Bgs\FlightLandingPages\Service\SiteConfigurationService:
    arguments:
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'

  # Repository configuration - repositories are auto-configured by default
  Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository:
    public: true

  # Controller configuration
  Bgs\FlightLandingPages\Controller\FlightReferenceController:
    public: true
    arguments:
      $flightRouteRepository: '@Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository'

  # URL Generation Service
  Bgs\FlightLandingPages\Service\UrlGenerationService:
    public: false
    arguments:
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'

  # Backend preview event listener
  Bgs\FlightLandingPages\EventListener\PagePreviewEventListener:
    public: false
    arguments:
      $flightRouteRepository: '@Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository'
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'
      $urlGenerationService: '@Bgs\FlightLandingPages\Service\UrlGenerationService'
    tags:
      - name: event.listener
        identifier: 'flight-landing-pages-preview'
        event: TYPO3\CMS\Backend\Controller\Event\ModifyPageLayoutContentEvent

  # Backend CSV export controller
  Bgs\FlightLandingPages\Controller\Backend\CsvExportController:
    public: true
    arguments:
      $urlGenerationService: '@Bgs\FlightLandingPages\Service\UrlGenerationService'

  # Console commands
  Bgs\FlightLandingPages\Command\UpdateSlugCommand:
    tags:
      - name: 'console.command'
        command: 'flight:update-slugs'
