<?php
return [
    'frontend' => [
        // Middleware 1: Virtual Route Detection (runs BEFORE PageResolver)
        'flight-landing-pages/virtual-route-detection' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteDetectionMiddleware::class,
            'before' => [
                'typo3/cms-frontend/page-resolver',
            ],
            'after' => [
                'typo3/cms-frontend/site',
            ],
        ],

        // Middleware 2: Virtual Route Enhancement (runs AFTER TypoScript preparation)
        'flight-landing-pages/virtual-route-enhancement' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteEnhancementMiddleware::class,
            'after' => [
                'typo3/cms-frontend/prepare-tsfe-rendering',
            ],
            'before' => [
                'typo3/cms-frontend/content-length-headers',
            ],
        ],

        // Disable the old middleware globally
        'flight-landing-pages/virtual-route' => [
            'disabled' => true,
        ],
    ],
];
