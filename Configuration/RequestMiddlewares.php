<?php
return [
    'frontend' => [
        // Middleware 1: Virtual Route Detection (runs BEFORE PageResolver)
        'flight-landing-pages/virtual-route-detection' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteDetectionMiddleware::class,
            'before' => [
                'typo3/cms-frontend/page-resolver',
            ],
            'after' => [
                'typo3/cms-frontend/site',
            ],
        ],

        // Middleware 2: Virtual Route Enhancement (runs AFTER TypoScript preparation)
        'flight-landing-pages/virtual-route-enhancement' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteEnhancementMiddleware::class,
            'after' => [
                'typo3/cms-frontend/prepare-tsfe-rendering',
            ],
            'before' => [
                'typo3/cms-frontend/content-length-headers',
            ],
        ],

        // Old middleware - disabled
        'flight-landing-pages/virtual-route' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteMiddleware::class,
            'disabled' => true, // Disabled in favor of Multiple Middlewares approach
        ],
    ],
];
